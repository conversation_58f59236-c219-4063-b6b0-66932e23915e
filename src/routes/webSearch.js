/**
 * @fileoverview Web Search Routes
 */

import express from 'express';
import { WebSearchController } from '../controllers/WebSearchController.js';
import { extractSessionId } from '../middleware/auth.js';
import { extractClientIP } from '../middleware/auth.js';
import { optionalAuth } from '../middleware/auth.js';
import { validateGuestChatSession } from '../middleware/guestChatValidation.js';
import { validate } from '../middleware/validation.js';
import { uploadSingleFile, handleFileUploadError } from '../middleware/fileUpload.js';
import { checkFileUploadLimits, consumeFileUploadLimit } from '../middleware/fileUploadLimits.js';
import Joi from 'joi';

const router = express.Router();

// Validation schemas
const validationSchemas = {
  webSearchQuery: {
    body: Joi.object({
      query: Joi.string().min(3).max(500).required().messages({
        'string.min': 'Search query must be at least 3 characters long',
        'string.max': 'Search query must be less than 500 characters',
        'any.required': 'Search query is required',
      }),
      llmModel: Joi.string().optional(),
    }),
  },
  webSearchQueryWithAttachment: {
    body: Joi.object({
      query: Joi.string().min(3).max(500).required().messages({
        'string.min': 'Search query must be at least 3 characters long',
        'string.max': 'Search query must be less than 500 characters',
        'any.required': 'Search query is required',
      }),
      llmModel: Joi.string().optional(),
    }),
  },
  simpleSearch: {
    body: Joi.object({
      query: Joi.string().min(3).max(500).required().messages({
        'string.min': 'Search query must be at least 3 characters long',
        'string.max': 'Search query must be less than 500 characters',
        'any.required': 'Search query is required',
      }),
    }),
  },
};

// Web search query endpoint (supports both authenticated and guest users)
router.post('/query',
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validateGuestChatSession,
  validate(validationSchemas.webSearchQuery),
  WebSearchController.searchQuery
);

// Web search query with file attachment endpoint
router.post('/query/attachment',
  uploadSingleFile,
  handleFileUploadError,
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validateGuestChatSession,
  checkFileUploadLimits,
  validate(validationSchemas.webSearchQueryWithAttachment),
  WebSearchController.searchQueryWithAttachment,
  consumeFileUploadLimit
);

// Get search history for current session
router.get('/history',
  extractSessionId,
  optionalAuth,
  WebSearchController.getSearchHistory
);

// Get web search service status
router.get('/status',
  WebSearchController.getServiceStatus
);

// Simple search endpoint (for testing, no streaming)
router.post('/simple',
  extractSessionId,
  extractClientIP,
  optionalAuth,
  validate(validationSchemas.simpleSearch),
  WebSearchController.simpleSearch
);

export default router;
