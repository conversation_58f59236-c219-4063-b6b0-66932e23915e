/**
 * @fileoverview Test script for Web Search functionality
 */

import dotenv from 'dotenv';
import { WebSearchService } from '../src/services/WebSearchService.js';
import { validateSearchQuery, processSearchResults } from '../src/utils/webSearchUtils.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config();

/**
 * Test web search functionality
 */
async function testWebSearch() {
  console.log('🔍 Testing Web Search Functionality\n');

  try {
    // Test 1: Initialize Web Search Service
    console.log('1. Initializing Web Search Service...');
    await WebSearchService.initialize();
    
    if (WebSearchService.isAvailable()) {
      console.log('✅ Web Search Service initialized successfully');
    } else {
      console.log('❌ Web Search Service not available');
      console.log('   Make sure TAVILY_API_KEY is set in your .env file');
      return;
    }

    // Test 2: Validate search queries
    console.log('\n2. Testing query validation...');
    
    const testQueries = [
      '',
      'hi',
      'What is artificial intelligence?',
      'A'.repeat(600), // Too long
    ];

    testQueries.forEach((query, index) => {
      const validation = validateSearchQuery(query);
      console.log(`   Query ${index + 1}: "${query.substring(0, 50)}${query.length > 50 ? '...' : ''}"`);
      console.log(`   Valid: ${validation.isValid ? '✅' : '❌'}`);
      if (!validation.isValid) {
        console.log(`   Error: ${validation.error}`);
      }
    });

    // Test 3: Perform actual web search
    console.log('\n3. Performing web search...');
    const searchQuery = 'latest developments in artificial intelligence 2024';
    console.log(`   Query: "${searchQuery}"`);
    
    try {
      const searchResults = await WebSearchService.performSearch(searchQuery);
      console.log(`   ✅ Found ${searchResults.length} search results`);
      
      if (searchResults.length > 0) {
        console.log('\n   Search Results:');
        searchResults.forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.title || 'Untitled'}`);
          console.log(`      URL: ${result.url}`);
          console.log(`      Snippet: ${(result.content || result.snippet || '').substring(0, 100)}...`);
        });
      }

      // Test 4: Process search results (extract content)
      console.log('\n4. Processing search results (extracting content)...');
      const processedResults = await processSearchResults(searchResults.slice(0, 2)); // Test with first 2 results
      console.log(`   ✅ Processed ${processedResults.length} results`);
      
      if (processedResults.length > 0) {
        console.log('\n   Processed Results:');
        processedResults.forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.extractedTitle}`);
          console.log(`      URL: ${result.url}`);
          console.log(`      Content Length: ${result.contentLength} characters`);
          console.log(`      Has Full Content: ${result.hasFullContent ? '✅' : '❌'}`);
          console.log(`      Content Preview: ${result.fullContent.substring(0, 150)}...`);
        });
      }

    } catch (searchError) {
      console.log(`   ❌ Search failed: ${searchError.message}`);
    }

    console.log('\n🎉 Web Search test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

/**
 * Test environment configuration
 */
function testEnvironment() {
  console.log('🔧 Checking Environment Configuration\n');

  const requiredEnvVars = [
    'TAVILY_API_KEY',
    'OPENAI_API_KEY', // For embeddings
    'PINECONE_API_KEY', // For storing results
  ];

  const optionalEnvVars = [
    'PINECONE_INDEX_NAME',
    'DEFAULT_LLM_MODEL',
  ];

  console.log('Required Environment Variables:');
  requiredEnvVars.forEach(envVar => {
    const value = process.env[envVar];
    console.log(`   ${envVar}: ${value ? '✅ Set' : '❌ Missing'}`);
  });

  console.log('\nOptional Environment Variables:');
  optionalEnvVars.forEach(envVar => {
    const value = process.env[envVar];
    console.log(`   ${envVar}: ${value ? `✅ ${value}` : '⚠️  Using default'}`);
  });

  console.log('\n');
}

/**
 * Main test function
 */
async function main() {
  console.log('🚀 Web Search Service Test Suite\n');
  console.log('='.repeat(50));

  // Test environment first
  testEnvironment();

  // Run web search tests
  await testWebSearch();

  console.log('\n' + '='.repeat(50));
  console.log('✨ All tests completed!');
}

// Run the tests
main().catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});
